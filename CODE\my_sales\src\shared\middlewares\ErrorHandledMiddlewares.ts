import { Request, Response, NextFunction } from 'express';
import AppError from '../error/AppError';

export default class ErrorHandledMiddleware {
  public static handleError(
    error: Error,
    _req: Request,
    resp: Response,
    _next: NextFunction
  ) {
    if (error instanceof AppError) {
      return resp.status(error.statusCode).json({
        type: 'error',
        message: error.message
      });
    }

    return resp.status(500).json({
      type: 'error',
      message: 'Internal Server Error'
    });
  }
}
