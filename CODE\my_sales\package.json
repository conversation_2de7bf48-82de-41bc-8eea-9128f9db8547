{"name": "my_sales", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "ts-node-dev -r tsconfig-paths/register --inspect --transpile-only --ignore-watch node_modules src/shared/http/server.ts", "lint": "eslint . --ext .ts", "lint-fix": "eslint . --ext .ts --fix", "migration:create": "node scripts/create-migration.js", "migration:run": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:run -d src/shared/typeorm/data-sources.ts", "migration:revert": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:revert -d src/shared/typeorm/data-sources.ts", "migration:show": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:show -d src/shared/typeorm/data-sources.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.0.3", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "cross-env": "^7.0.3", "eslint": "^9.29.0", "globals": "^16.2.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-async-errors": "^3.1.1", "pg": "^8.16.2", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.25"}}