const { execSync } = require('child_process');
const path = require('path');

// Get the migration name from command line arguments or npm config
let migrationName = process.argv[2];

// If no direct argument, try to get from npm config (for --name= syntax)
if (!migrationName && process.env.npm_config_name) {
  migrationName = process.env.npm_config_name;
}

if (!migrationName) {
  console.error('Error: Migration name is required');
  console.log('Usage: npm run migration:create <MigrationName>');
  console.log('   or: npm run migration:create --name=<MigrationName>');
  process.exit(1);
}

// Build the migration path
const migrationPath = path.join('src', 'shared', 'typeorm', 'migrations', migrationName);

// Build the command
const command = `ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:create ${migrationPath}`;

try {
  console.log(`Creating migration: ${migrationName}`);
  execSync(command, { stdio: 'inherit' });
  console.log(`Migration ${migrationName} created successfully!`);
} catch (error) {
  console.error('Error creating migration:', error.message);
  process.exit(1);
}
